{"name": "xitools-mcp-client", "version": "1.0.0", "description": "XItools MCP客户端 - 支持API Key认证的MCP客户端包装器", "main": "xitools-mcp-client.js", "bin": {"xitools-mcp": "./xitools-mcp-client.js"}, "scripts": {"start": "node xitools-mcp-client.js", "config": "node xitools-mcp-client.js --config", "install-global": "npm install -g .", "test": "node xitools-mcp-client.js --help"}, "keywords": ["mcp", "model-context-protocol", "xitools", "task-management", "api-client"], "author": "XItools Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/xitools.git", "directory": "mcp-client"}, "bugs": {"url": "https://github.com/your-username/xitools/issues"}, "homepage": "https://xitools.furdow.com"}