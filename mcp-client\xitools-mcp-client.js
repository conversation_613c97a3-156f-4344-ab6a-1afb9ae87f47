#!/usr/bin/env node

/**
 * XItools MCP客户端 - 简化版本
 *
 * 这是一个简单的MCP stdio传输客户端，无需安装依赖
 * 直接与XItools的HTTP API通信
 *
 * 使用方法：
 * 1. 在XItools网站上生成API Key
 * 2. 设置环境变量 XITOOLS_API_KEY
 * 3. 在Claude Desktop等MCP客户端中配置此脚本
 */

const https = require('https');
const http = require('http');

// 配置
const SERVER_URL = process.env.XITOOLS_SERVER_URL || 'https://xitools.furdow.com';
const API_KEY = process.env.XITOOLS_API_KEY;

/**
 * 配置管理
 */
class ConfigManager {
  constructor() {
    this.config = this.loadConfig();
  }

  loadConfig() {
    try {
      if (fs.existsSync(CONFIG_FILE)) {
        return JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
      }
    } catch (error) {
      console.error('加载配置文件失败:', error.message);
    }

    // 默认配置
    return {
      serverUrl: process.env.XITOOLS_SERVER_URL || 'https://xitools.furdow.com',
      apiKey: process.env.XITOOLS_API_KEY || '',
      timeout: 30000,
    };
  }

  saveConfig(config) {
    try {
      fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
      this.config = config;
    } catch (error) {
      console.error('保存配置文件失败:', error.message);
    }
  }

  getConfig() {
    return this.config;
  }
}

/**
 * XItools MCP客户端
 */
class XitoolsMcpClient {
  constructor() {
    this.configManager = new ConfigManager();
    this.config = this.configManager.getConfig();
    
    if (!this.config.apiKey) {
      console.error('错误: 未配置API Key');
      console.error('请设置环境变量 XITOOLS_API_KEY 或运行配置命令');
      process.exit(1);
    }

    this.server = new Server(
      {
        name: 'xitools-mcp-client',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupTools();
  }

  /**
   * 设置MCP工具
   */
  setupTools() {
    // 获取远程工具列表并注册
    this.registerRemoteTools();
  }

  /**
   * 注册远程工具
   */
  async registerRemoteTools() {
    try {
      const response = await this.makeRequest('tools/list', {});
      const tools = response.tools || [];

      for (const tool of tools) {
        this.server.setRequestHandler({ method: `tools/call`, name: tool.name }, async (request) => {
          return await this.callRemoteTool(tool.name, request.params.arguments || {});
        });
      }

      console.log(`已注册 ${tools.length} 个XItools工具`);
    } catch (error) {
      console.error('注册远程工具失败:', error.message);
    }
  }

  /**
   * 调用远程工具
   */
  async callRemoteTool(toolName, args) {
    try {
      const result = await this.makeRequest('tools/call', {
        name: toolName,
        arguments: args,
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`调用工具 ${toolName} 失败: ${error.message}`);
    }
  }

  /**
   * 发送请求到XItools服务器
   */
  async makeRequest(method, params) {
    const url = `${this.config.serverUrl}/mcp-auth`;
    
    const requestData = {
      jsonrpc: '2.0',
      method: method,
      params: params,
      id: Date.now(),
    };

    try {
      const response = await axios.post(url, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
          'User-Agent': 'XItools-MCP-Client/1.0.0',
        },
        timeout: this.config.timeout,
      });

      if (response.data.error) {
        throw new Error(response.data.error.message || '服务器返回错误');
      }

      return response.data.result;
    } catch (error) {
      if (error.response) {
        throw new Error(`HTTP ${error.response.status}: ${error.response.data?.message || error.message}`);
      } else if (error.request) {
        throw new Error('无法连接到XItools服务器');
      } else {
        throw new Error(error.message);
      }
    }
  }

  /**
   * 启动MCP服务器
   */
  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.log('XItools MCP客户端已启动');
  }
}

/**
 * 配置命令处理
 */
function handleConfigCommand() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const configManager = new ConfigManager();
  const currentConfig = configManager.getConfig();

  console.log('XItools MCP客户端配置');
  console.log('当前配置:', JSON.stringify(currentConfig, null, 2));
  console.log('');

  rl.question(`服务器URL (${currentConfig.serverUrl}): `, (serverUrl) => {
    rl.question(`API Key (${currentConfig.apiKey ? '已设置' : '未设置'}): `, (apiKey) => {
      rl.question(`超时时间/毫秒 (${currentConfig.timeout}): `, (timeout) => {
        const newConfig = {
          serverUrl: serverUrl || currentConfig.serverUrl,
          apiKey: apiKey || currentConfig.apiKey,
          timeout: parseInt(timeout) || currentConfig.timeout,
        };

        configManager.saveConfig(newConfig);
        console.log('配置已保存到:', CONFIG_FILE);
        rl.close();
      });
    });
  });
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--config') || args.includes('-c')) {
    handleConfigCommand();
    return;
  }

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
XItools MCP客户端

用法:
  node xitools-mcp-client.js          启动MCP服务器
  node xitools-mcp-client.js --config 配置客户端
  node xitools-mcp-client.js --help   显示帮助

环境变量:
  XITOOLS_SERVER_URL  XItools服务器URL (默认: https://xitools.furdow.com)
  XITOOLS_API_KEY     XItools API Key (必需)

配置文件: ${CONFIG_FILE}
`);
    return;
  }

  try {
    const client = new XitoolsMcpClient();
    await client.start();
  } catch (error) {
    console.error('启动失败:', error.message);
    process.exit(1);
  }
}

// 启动应用
if (require.main === module) {
  main().catch((error) => {
    console.error('未处理的错误:', error);
    process.exit(1);
  });
}
